"""
Debug Queue Processor for testing and debugging JIRA data processing issues.

This module provides utilities to debug data processing issues in the consume_* functions
by fetching real JIRA data and testing the DataFrame creation and transformation steps.
"""

import asyncio
import pandas as pd
import traceback
import sys
import json
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import (
    LoggerContainer, QueueContainer,
    JiraEntryDetailsContainer, IssueFieldsContainer, EntryDetails, KeePassContainer, DatabaseContainer
)
from dags.data_pipeline.utility_code import (
    consume_changelog,
    consume_worklog, consume_comment, consume_issue_links, consume_issue
)
from dags.data_pipeline.database.upsert_operations import upsert_async
from dags.data_pipeline.jira.api_client import fetch_with_retries_post

from logging import Logger


class JiraDataFetcher:
    """Fetches JIRA data for debugging purposes."""

    def __init__(self, jira_entry: EntryDetails, logger: Logger):
        self.jira_entry = jira_entry
        self.logger = logger
        self.base_url = jira_entry.url
        self.headers = jira_entry.custom_properties

    async def fetch_issues_by_jql(
        self,
        jql: str,
        max_results: int = 10,
        fields: List[str] = None
    ) -> List[Dict]:
        """Fetch issues from JIRA using JQL."""
        if fields is None:
            # Default fields that include the data we need for debugging
            fields = [
                "id", "key", "summary", "description", "status", "issuetype",
                "assignee", "reporter", "created", "updated", "parent",
                "worklog", "comment", "issuelinks", "changelog"
            ]

        payload = {
            "jql": jql,
            "fields": fields,
            "startAt": 0,
            "maxResults": max_results,
            "expand": ["changelog", "renderedFields"]
        }

        url = f"{self.base_url}/rest/api/3/search"

        async with aiohttp.ClientSession(headers=self.headers) as session:
            try:
                self.logger.info(f"Fetching JIRA data with JQL: {jql}")
                response = await fetch_with_retries_post(session, url, payload)

                if response.get('success'):
                    issues = response['result']['issues']
                    self.logger.info(f"Fetched {len(issues)} issues from JIRA")
                    return issues
                else:
                    self.logger.error(f"Failed to fetch JIRA data: {response.get('exception')}")
                    return []

            except Exception as e:
                self.logger.error(f"Exception fetching JIRA data: {e}", exc_info=True)
                return []


class QueueDebugProcessor:
    """Debug processor for JIRA data processing issues."""

    def __init__(self, logger: Logger, process_data: bool = False):
        """
        Initialize the debug processor.

        Args:
            logger: Logger instance for debugging output
            process_data: If True, actually process data to database. If False, just inspect.
        """
        self.logger = logger
        self.process_data = process_data
        self.processed_count = 0
        self.error_count = 0
        self.item_types = {}
        self.raw_data_samples = []
        
    def log_raw_jira_data(self, issues: List[Dict], context: str = ""):
        """Log detailed information about raw JIRA data structure."""
        try:
            self.logger.info(f"=== Raw JIRA Data Analysis {context} ===")
            self.logger.info(f"Number of issues: {len(issues)}")

            if not issues:
                self.logger.warning("No issues to analyze")
                return

            # Analyze first issue structure
            first_issue = issues[0]
            self.logger.info(f"Issue keys: {list(first_issue.keys())}")

            # Check fields structure
            if 'fields' in first_issue:
                fields = first_issue['fields']
                self.logger.info(f"Fields available: {list(fields.keys())}")

                # Check specific problematic fields
                for field_name in ['worklog', 'comment', 'changelog', 'issuelinks']:
                    if field_name in first_issue or (field_name in fields):
                        field_data = first_issue.get(field_name) or fields.get(field_name)
                        if field_data:
                            self.logger.info(f"{field_name} structure: {type(field_data)}")
                            if isinstance(field_data, dict):
                                self.logger.info(f"{field_name} keys: {list(field_data.keys())}")
                            elif isinstance(field_data, list) and field_data:
                                self.logger.info(f"{field_name} first item type: {type(field_data[0])}")
                                if isinstance(field_data[0], dict):
                                    self.logger.info(f"{field_name} first item keys: {list(field_data[0].keys())}")

            # Store sample for later analysis
            self.raw_data_samples.append({
                'context': context,
                'sample_issue': first_issue,
                'timestamp': datetime.now()
            })

        except Exception as e:
            self.logger.error(f"Error logging raw JIRA data: {e}", exc_info=True)

    def log_dataframe_info(self, df: pd.DataFrame, context: str = ""):
        """Log detailed information about a DataFrame."""
        try:
            self.logger.info(f"=== DataFrame Info {context} ===")
            self.logger.info(f"Shape: {df.shape}")
            self.logger.info(f"Columns: {list(df.columns)}")
            self.logger.info(f"Data types:\n{df.dtypes}")

            # Check for null values
            null_counts = df.isnull().sum()
            if null_counts.any():
                self.logger.info(f"Null values:\n{null_counts[null_counts > 0]}")

            # Show first few rows (but limit output)
            if df.shape[0] > 0:
                self.logger.info(f"First 3 rows:\n{df.head(3).to_string()}")

            # Check for problematic data types
            for col in df.columns:
                if df[col].dtype == 'object':
                    unique_types = df[col].apply(type).value_counts()
                    if len(unique_types) > 1:
                        self.logger.warning(f"Column '{col}' has mixed types: {unique_types}")

        except Exception as e:
            self.logger.error(f"Error logging DataFrame info: {e}")
    
    def log_queue_item_info(self, item: Dict[str, Any]):
        """Log detailed information about a queue item."""
        try:
            self.logger.info("=== Queue Item Info ===")
            self.logger.info(f"Keys: {list(item.keys())}")
            
            if "model" in item:
                model_name = item["model"].__name__ if hasattr(item["model"], "__name__") else str(item["model"])
                self.logger.info(f"Model: {model_name}")
                
                # Track model types
                if model_name not in self.item_types:
                    self.item_types[model_name] = 0
                self.item_types[model_name] += 1
            
            if "df" in item and isinstance(item["df"], pd.DataFrame):
                self.log_dataframe_info(item["df"], f"for {model_name}")
            
            # Log other parameters
            for key, value in item.items():
                if key not in ["model", "df"]:
                    self.logger.info(f"{key}: {value}")
                    
        except Exception as e:
            self.logger.error(f"Error logging queue item info: {e}")
    
    async def process_queue_item(self, item: Dict[str, Any], pg_async_session=None) -> bool:
        """
        Process a single queue item with detailed logging.
        
        Args:
            item: Queue item dictionary
            pg_async_session: Database session (if processing data)
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            self.processed_count += 1
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"Processing item #{self.processed_count}")
            self.logger.info(f"Timestamp: {datetime.now()}")
            
            # Log item details
            self.log_queue_item_info(item)
            
            if self.process_data and pg_async_session:
                # Actually process the data
                model = item["model"]
                df = item["df"]
                no_update_cols = item.get("no_update_cols", ())
                on_conflict_update = item.get("on_conflict_update", True)
                conflict_condition = item.get("conflict_condition", None)
                
                self.logger.info("Attempting to upsert data...")
                await upsert_async(
                    pg_async_session, model, df,
                    no_update_cols=no_update_cols,
                    on_conflict_update=on_conflict_update,
                    conflict_condition=conflict_condition,
                    my_logger=self.logger
                )
                self.logger.info("Upsert completed successfully!")
            else:
                self.logger.info("INSPECTION MODE - Not processing data to database")
            
            return True
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error processing queue item #{self.processed_count}: {e}")
            self.logger.error(f"Exception type: {type(e).__name__}")
            self.logger.error(f"Traceback:\n{traceback.format_exc()}")
            
            # Try to log DataFrame info even if processing failed
            try:
                if "df" in item and isinstance(item["df"], pd.DataFrame):
                    self.logger.error("DataFrame that caused the error:")
                    self.log_dataframe_info(item["df"], "ERROR CONTEXT")
            except:
                pass
                
            return False
    
    async def test_consume_function_with_real_data(
        self,
        issues: List[Dict],
        consume_function_name: str,
        queue_manager,
        pg_async_session=None,
        http_session=None
    ):
        """Test a specific consume function with real JIRA data."""
        try:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"Testing {consume_function_name} with real JIRA data")
            self.logger.info(f"{'='*60}")

            # Log raw data structure
            self.log_raw_jira_data(issues, f"for {consume_function_name}")

            # Create DataFrame from issues (simulating what IssueProcessor does)
            df_issue = pd.DataFrame(issues)
            self.log_dataframe_info(df_issue, "Initial DataFrame from JIRA")

            # Extract specific data based on consume function
            if consume_function_name == "consume_changelog":
                df_specific = df_issue[["id", "key", 'changelog']].copy()
                queue_name = "queue_changelog"
            elif consume_function_name == "consume_worklog":
                df_issue['worklog'] = df_issue['fields'].apply(lambda x: x.get('worklog'))
                df_specific = df_issue[['worklog', "id", "key"]].copy()
                queue_name = "queue_worklog"
            elif consume_function_name == "consume_comment":
                df_issue['comment'] = df_issue['fields'].apply(lambda x: x.get('comment'))
                df_specific = df_issue[['comment', "id", "key"]].copy()
                queue_name = "queue_comment"
            elif consume_function_name == "consume_issue_links":
                if 'issuelinks' in df_issue.columns:
                    df_specific = df_issue[['issuelinks', "id", "key"]].copy()
                    df_specific.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
                else:
                    self.logger.warning("No issuelinks column found in data")
                    return False
                queue_name = "queue_issue_links"
            elif consume_function_name == "consume_issue":
                df_specific = df_issue.copy()
                queue_name = "queue_issue"
            else:
                self.logger.error(f"Unknown consume function: {consume_function_name}")
                return False

            self.log_dataframe_info(df_specific, f"Extracted data for {consume_function_name}")

            # Put data into the appropriate queue
            queues = queue_manager.get_queues()
            await queues[queue_name].put(df_specific)

            # Add termination signal
            await queues[queue_name].put(None)

            self.logger.info(f"Data queued for {consume_function_name}")

            if self.process_data and pg_async_session:
                # Actually run the consume function
                self.logger.info(f"Running {consume_function_name}...")

                if consume_function_name == "consume_changelog":
                    await consume_changelog(
                        queue_id=999,
                        name="debug_changelog",
                        queue_changelog=queues[queue_name],
                        queue_upsert_issue=queues["queue_upsert_others"],
                        project_key="DEBUG",
                        http_session=http_session
                    )
                elif consume_function_name == "consume_worklog":
                    await consume_worklog(
                        queue_id=999,
                        name="debug_worklog",
                        queue_worklog=queues[queue_name],
                        queue_upsert_issue=queues["queue_upsert_others"],
                        project_key="DEBUG",
                        http_session=http_session
                    )
                elif consume_function_name == "consume_comment":
                    await consume_comment(
                        queue_id=999,
                        queue_comment=queues[queue_name],
                        queue_upsert_issue=queues["queue_upsert_others"],
                        project_key="DEBUG",
                        http_session=http_session
                    )
                elif consume_function_name == "consume_issue_links":
                    await consume_issue_links(
                        queue_id=999,
                        name="debug_issue_links",
                        queue_issue_links=queues[queue_name],
                        queue_upsert_issue=queues["queue_upsert_others"],
                        project_key="DEBUG"
                    )
                elif consume_function_name == "consume_issue":
                    await consume_issue(
                        queue_id=999,
                        name="debug_issue",
                        queue_issue=queues[queue_name],
                        queue_upsert_issue=queues["queue_upsert_issue"],  # consume_issue still writes to queue_upsert_issue
                        project_key="DEBUG"
                    )

                self.logger.info(f"{consume_function_name} completed successfully!")
            else:
                self.logger.info("INSPECTION MODE - Not running actual consume function")

            return True

        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error testing {consume_function_name}: {e}", exc_info=True)
            return False

    def log_summary(self):
        """Log processing summary."""
        self.logger.info(f"\n{'='*50}")
        self.logger.info("PROCESSING SUMMARY")
        self.logger.info(f"Total items processed: {self.processed_count}")
        self.logger.info(f"Successful: {self.processed_count - self.error_count}")
        self.logger.info(f"Errors: {self.error_count}")
        self.logger.info(f"Item types processed: {self.item_types}")

        if self.raw_data_samples:
            self.logger.info(f"Raw data samples collected: {len(self.raw_data_samples)}")
            for i, sample in enumerate(self.raw_data_samples):
                self.logger.info(f"Sample {i+1}: {sample['context']} at {sample['timestamp']}")


@inject
async def debug_consume_functions_with_jira_data(
    project_key: str = "PLAT",
    jql_override: str = None,
    max_issues: int = 5,
    consume_functions: List[str] = None,
    process_data: bool = False,
    my_logger: Logger = Provide[LoggerContainer.logger],
    jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
    q_container = Provide[QueueContainer],
    db_container = Provide[DatabaseContainer]
):
    """
    Debug consume functions with real JIRA data.

    Args:
        project_key: JIRA project key to fetch data from
        jql_override: Custom JQL query (if None, uses default)
        max_issues: Maximum number of issues to fetch
        consume_functions: List of consume functions to test (if None, tests all)
        process_data: If True, actually process data to database
        my_logger: Logger instance
        jira_entry: JIRA connection details
        q_container: Queue container
        db_container: Application container
    """


    if consume_functions is None:
        consume_functions = [
            "consume_changelog", "consume_worklog", "consume_comment",
            "consume_issue_links", "consume_issue"
        ]


    # Build JQL query
    if jql_override:
        jql = jql_override
    else:
        # Get recent issues from the project
        recent_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        jql = f"project = {project_key} AND updated > '{recent_date}' ORDER BY updated DESC"

    my_logger.info(f"Starting JIRA consume function debug with JQL: {jql}")

    # Initialize components
    fetcher = JiraDataFetcher(jira_entry, my_logger)
    processor = QueueDebugProcessor(my_logger, process_data)

    # Configure queue container
    q_container.config.override({"schema_name": project_key.lower()})
    queue_manager = QueueManager(q_container)

    # Fetch JIRA data
    issues = await fetcher.fetch_issues_by_jql(jql, max_issues)

    if not issues:
        my_logger.error("No issues fetched from JIRA. Cannot proceed with testing.")
        return

    # Get database session if needed
    pg_async_session = None
    http_session = None

    if process_data:
        db_manager = db_container.async_session_managers()["public"]
        pg_async_session = await db_manager.async_session().__aenter__()
        http_session = aiohttp.ClientSession(headers=jira_entry.custom_properties)

    try:
        # Test each consume function
        for func_name in consume_functions:
            my_logger.info(f"\n{'='*80}")
            my_logger.info(f"TESTING {func_name.upper()}")
            my_logger.info(f"{'='*80}")

            success = await processor.test_consume_function_with_real_data(
                issues=issues,
                consume_function_name=func_name,
                queue_manager=queue_manager,
                pg_async_session=pg_async_session,
                http_session=http_session
            )

            if success:
                my_logger.info(f"✅ {func_name} test completed successfully")
            else:
                my_logger.error(f"❌ {func_name} test failed")


            # Small delay between tests
            await asyncio.sleep(1)

    finally:
        if pg_async_session:
            await pg_async_session.__aexit__(None, None, None)
        if http_session:
            await http_session.close()

        processor.log_summary()


# Helper class for queue management
class QueueManager:
    """Manages queue access for debugging."""

    def __init__(self, q_container):
        self.q_container = q_container

    def get_queues(self):
        """Get all queues."""
        return self.q_container.queue_selector()



async def debug_specific_consume_function(
    consume_function_name: str,
    project_key: str = "PLAT",
    jql_override: str = None,
    max_issues: int = 3,
    process_data: bool = False,
):
    """
    Debug a specific consume function with real JIRA data.

    Args:
        consume_function_name: Name of the consume function to test
        project_key: JIRA project key
        jql_override: Custom JQL query
        max_issues: Maximum number of issues to fetch
        process_data: If True, actually process data to database
    """
    await debug_consume_functions_with_jira_data(
        project_key=project_key,
        jql_override=jql_override,
        max_issues=max_issues,
        consume_functions=[consume_function_name],
        process_data=process_data,
    )


# Convenience functions for easy testing

async def simple_jira_debug_test():
    """Simple test function that can be called directly."""
    # Wire the container
    # container = ApplicationContainer()
    # container.wire(modules=[__name__])

    # Test all consume functions with PLAT project data


    await debug_consume_functions_with_jira_data(
        project_key="PLAT",
        max_issues=3,
        process_data=False,  # Just inspect, don't process to DB
    )


async def debug_specific_function_test(function_name: str, project_key: str = "PLAT"):
    """Debug a specific consume function."""
    # container = ApplicationContainer()
    # container.wire(modules=[__name__])

    await debug_specific_consume_function(
        consume_function_name=function_name,
        project_key=project_key,
        max_issues=2,
        process_data=False
    )


async def debug_with_custom_jql(jql: str, project_key: str = "PLAT"):
    """Debug with a custom JQL query."""
    # container = ApplicationContainer()
    # container.wire(modules=[__name__])

    await debug_consume_functions_with_jira_data(
        project_key=project_key,
        jql_override=jql,
        max_issues=5,
        process_data=False
    )

keepass_container = KeePassContainer()

logger_container = LoggerContainer()
logger_container.wire(modules=[__name__])
logger_container.init_resources()

# Ensure correlation factory is installed
from dags.data_pipeline.logging_utils import install_correlation_log_record_factory
install_correlation_log_record_factory()

jira_container = JiraEntryDetailsContainer()
# jira_container.wire([__name__])
jira_container.init_resources()

extract_container = IssueFieldsContainer()

db_container = DatabaseContainer()
db_container.wire(modules=[__name__])

queue_container = QueueContainer()
queue_container.wire([__name__])


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "specific" and len(sys.argv) > 2:
            # python debug_queue_processor.py specific consume_changelog
            function_name = sys.argv[2]
            project = sys.argv[3] if len(sys.argv) > 3 else "PLAT"
            asyncio.run(debug_specific_function_test(function_name, project))
        elif sys.argv[1] == "jql" and len(sys.argv) > 2:
            # python debug_queue_processor.py jql "project = PLAT AND key = PLAT-1234"
            jql = sys.argv[2]
            project = sys.argv[3] if len(sys.argv) > 3 else "PLAT"
            asyncio.run(debug_with_custom_jql(jql, project))
        else:
            print("Usage:")
            print("  python debug_queue_processor.py")
            print("  python debug_queue_processor.py specific consume_changelog [PROJECT]")
            print("  python debug_queue_processor.py jql 'JQL_QUERY' [PROJECT]")
    else:
        # Run the simple test
        asyncio.run(simple_jira_debug_test())
